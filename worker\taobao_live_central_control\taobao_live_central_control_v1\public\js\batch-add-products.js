/**
 * 批量上品功能 - 前端UI交互
 */

// 全局变量
let currentLiveId = '';
let currentAnchorName = '';

/**
 * 显示批量上品弹窗
 * @param {string} liveId 直播ID
 * @param {string} anchorName 主播名称
 */
window.showBatchAddProductsModal = function(liveId, anchorName) {
    currentLiveId = liveId;
    currentAnchorName = anchorName;

    const modalHtml = `
        <div id="batchAddProductsModal" class="fixed z-50 inset-0 overflow-y-auto">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                    批量上品 - ${anchorName} (${liveId})
                                </h3>

                                <!-- 统计信息 -->
                                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                                    <div class="flex justify-between text-sm">
                                        <span>当前最新数量: <span id="currentCount" class="font-medium text-blue-600">0</span> 个产品</span>
                                        <span>成功: <span id="add_successCount" class="font-medium text-green-600">0</span> 个</span>
                                        <span>失败: <span id="add_failedCount" class="font-medium text-red-600">0</span> 个</span>
                                    </div>
                                </div>

                                <!-- 产品ID输入框 -->
                                <div class="mb-4">
                                    <label for="productIds" class="block text-sm font-medium text-gray-700 mb-2">
                                        产品ID列表（一行一个）
                                    </label>
                                    <textarea id="productIds" rows="10"
                                        class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                                        placeholder="请输入产品ID，一行一个，例如：&#10;724038386814&#10;846631525226&#10;904461720819"></textarea>
                                </div>


                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button id="startBatchAddBtn" type="button"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm">
                            <i class="fas fa-plus mr-2"></i>开始批量添加
                        </button>
                        <button id="cancelBatchAddBtn" type="button"
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    $('body').append(modalHtml);

    // 绑定事件
    $('#startBatchAddBtn').on('click', startBatchAddProducts);
    $('#cancelBatchAddBtn').on('click', closeBatchAddProductsModal);

    // 点击背景关闭
    $('#batchAddProductsModal').on('click', function(e) {
        if (e.target === this) {
            closeBatchAddProductsModal();
        }
    });

    // 获取当前产品数量
    extractProductsAfterAdd();
};

/**
 * 关闭批量上品弹窗
 */
function closeBatchAddProductsModal() {
    $('#batchAddProductsModal').remove();
}

/**
 * 开始批量添加产品
 */
async function startBatchAddProducts() {
    const productIdsText = $('#productIds').val().trim();

    if (!productIdsText) {
        showMessage('请输入产品ID', 'warning');
        return;
    }

    // 解析产品ID列表
    const productIds = productIdsText.split('\n')
        .map(id => id.trim())
        .filter(id => id && /^\d+$/.test(id));

    if (productIds.length === 0) {
        showMessage('请输入有效的产品ID', 'warning');
        return;
    }

    // 初始化统计信息（只重置成功和失败数量）
    updateStatsDisplay(0, 0);

    // 禁用按钮
    $('#startBatchAddBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>添加中...');

    try {
        // 调用后端API
        const response = await apiRequest('/api/batch-add-products', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                liveId: currentLiveId,
                anchorName: currentAnchorName,
                productIds: productIds
            })
        });

        if (response.success) {
            console.log('前端收到响应:', response);
            console.log('成功数量:', response.successCount, '失败数量:', response.failedCount);

            // 1. 先显示成功和失败数量
            updateStatsDisplay(response.successCount, response.failedCount);

            showMessage(`批量添加完成！成功: ${response.successCount}，失败: ${response.failedCount}`, 'success');

            // 2. 然后提取最新产品数据，只更新当前最新数量
             await extractProductsAfterAdd();

            // 3. 清空输入框
            $('#productIds').val('');
        } else {
            showMessage('批量添加失败: ' + response.error, 'error');
        }

    } catch (error) {
        console.error('批量添加失败:', error);
        showMessage('批量添加失败: ' + error.message, 'error');
    } finally {
        $('#startBatchAddBtn').prop('disabled', false).html('<i class="fas fa-plus mr-2"></i>开始批量添加');
    }
}

/**
 * 添加完成后提取产品数据
 */
async function extractProductsAfterAdd() {
    try {
        const response = await apiRequest(`/api/live-products/sync`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                liveId: currentLiveId
            })
        });

        if (response.success) {
            console.log('产品数据提取成功:', response);

            // 只更新当前最新数量，不刷新页面（保持成功失败数量显示）
            if (response.total !== undefined) {
                updateCurrentCount(response.total);
            } else if (response.data && response.data.total !== undefined) {
                updateCurrentCount(response.data.total);
            }

            console.log('当前最新数量已更新，保持成功失败数量不变');
        }
    } catch (error) {
        console.error('提取产品数据失败:', error);
    }
}

/**
 * 更新统计信息显示
 */
function updateStatsDisplay(success, failed) {
    console.log('更新统计显示 - 成功:', success, '失败:', failed);

    $('#add_successCount').text(success);
    $('#add_failedCount').text(failed);
}

/**
 * 更新当前最新数量
 */
function updateCurrentCount(count) {
    console.log('更新当前最新数量:', count);
    $('#currentCount').text(count);
    console.log('DOM更新后 - 当前数量元素:', $('#currentCount').text());
}

/**
 * 获取当前产品数量
 */
async function getCurrentProductCount() {
    try {
        const response = await apiRequest(`/api/live-products?liveId=${currentLiveId}`, {
            method: 'GET'
        });

        if (response.success && response.products) {
            const count = response.products.length;
            console.log('当前产品数量:', count);
            updateCurrentCount(count);
        } else {
            console.log('获取产品数量失败:', response);
            updateCurrentCount(0);
        }
    } catch (error) {
        console.error('获取产品数量失败:', error);
        updateCurrentCount(0);
    }
}

// 页面加载完成后的初始化
$(document).ready(function() {
    console.log('批量上品模块加载完成');
});
